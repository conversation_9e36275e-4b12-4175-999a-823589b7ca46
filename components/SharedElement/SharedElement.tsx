import React from 'react';
import { View } from 'react-native';
import { useSharedElement } from '@/hooks/useSharedElement';
import { SharedElementProps } from './types';

/**
 * SharedElement component - Wraps content to enable shared element transitions
 * 
 * @param id - Unique identifier for the shared element
 * @param children - Content to be wrapped
 * @param style - Additional styles to apply
 * @param accessibilityLabel - Accessibility label for screen readers
 */
export const SharedElement: React.FC<SharedElementProps> = ({ 
  id, 
  children, 
  style,
  accessibilityLabel 
}) => {
  const { viewRef } = useSharedElement(id, [children]);
  
  return (
    <View 
      ref={viewRef} 
      style={style}
      accessibilityLabel={accessibilityLabel}
      accessible={!!accessibilityLabel}
    >
      {children}
    </View>
  );
};