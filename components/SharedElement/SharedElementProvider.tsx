import React, { createContext, useContext, useState, useCallback, useMemo } from 'react';
import { SharedElementContextType, SharedElementData, TransitionData } from './types';

const SharedElementContext = createContext<SharedElementContextType | null>(null);

/**
 * SharedElementProvider - Manages shared element transitions across screens
 * 
 * @param children - React components to wrap with shared element functionality
 */
export const SharedElementProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [sharedElements, setSharedElements] = useState<Record<string, SharedElementData>>({});
  const [activeTransition, setActiveTransition] = useState<{ fromId: string; toId: string } | null>(null);
  const [transitionData, setTransitionData] = useState<TransitionData | null>(null);

  const registerElement = useCallback((id: string, element: SharedElementData) => {
    setSharedElements(prev => ({ ...prev, [id]: element }));
  }, []);

  const unregisterElement = useCallback((id: string) => {
    setSharedElements(prev => {
      const newElements = { ...prev };
      delete newElements[id];
      return newElements;
    });
  }, []);

  const startTransition = useCallback((fromId: string, toId: string, callback: () => void) => {
    const fromElement = sharedElements[fromId];
    const toElement = sharedElements[toId];
    
    if (fromElement && toElement) {
      setTransitionData({
        from: fromElement,
        to: toElement,
        fromId,
        toId,
      });
      setActiveTransition({ fromId, toId });
      callback();
    } else {
      console.warn(`Shared element not found: ${fromId} or ${toId}`);
      callback();
    }
  }, [sharedElements]);

  const endTransition = useCallback(() => {
    setActiveTransition(null);
    setTransitionData(null);
  }, []);

  const contextValue = useMemo(() => ({
    registerElement,
    unregisterElement,
    startTransition,
    endTransition,
    activeTransition,
    transitionData,
    sharedElements,
  }), [
    registerElement,
    unregisterElement,
    startTransition,
    endTransition,
    activeTransition,
    transitionData,
    sharedElements,
  ]);

  return (
    <SharedElementContext.Provider value={contextValue}>
      {children}
    </SharedElementContext.Provider>
  );
};

export const useSharedElementContext = () => {
  const context = useContext(SharedElementContext);
  if (!context) {
    throw new Error('useSharedElementContext must be used within SharedElementProvider');
  }
  return context;
};