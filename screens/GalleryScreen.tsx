import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Image } from 'expo-image';
import React, { useCallback, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';

import { SharedElement } from '@/components/SharedElement/SharedElement';
import { useSharedElementContext } from '@/components/SharedElement/SharedElementProvider';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { ImageData, RootStackParamList } from '@/types/navigation';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

type GalleryScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Main'>;

/**
 * GalleryScreen - Displays a grid of images with shared element transition support
 */
export default function GalleryScreen() {
  const navigation = useNavigation<GalleryScreenNavigationProp>();
  const context = useSharedElementContext();
  const colorScheme = useColorScheme();
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<Record<string, boolean>>({});

  // Production-ready image URLs with proper error handling
  const images = useMemo((): ImageData[] => [
    { 
      id: 'img1', 
      url: 'https://images.unsplash.com/photo-1507146426996-ef05306b995a?w=400&h=400&fit=crop',
      title: 'Mountain Lake',
      description: 'Beautiful mountain lake reflection'
    },
    { 
      id: 'img2', 
      url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=400&fit=crop',
      title: 'Forest Path',
      description: 'Serene forest walking path'
    },
    { 
      id: 'img3', 
      url: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=400&h=400&fit=crop',
      title: 'Ocean Sunset',
      description: 'Golden hour over the ocean'
    },
    { 
      id: 'img4', 
      url: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=400&fit=crop',
      title: 'Desert Landscape',
      description: 'Vast desert under blue sky'
    },
    { 
      id: 'img5', 
      url: 'https://images.unsplash.com/photo-1501785888041-af3ef285b470?w=400&h=400&fit=crop',
      title: 'Rolling Hills',
      description: 'Green rolling hills landscape'
    },
    { 
      id: 'img6', 
      url: 'https://images.unsplash.com/photo-1475924156734-496f6cac6ec1?w=400&h=400&fit=crop',
      title: 'Coastal Cliffs',
      description: 'Dramatic coastal cliff formation'
    },
  ], []);

  const handleImagePress = useCallback((image: ImageData) => {
    if (errors[image.id]) {
      Alert.alert('Error', 'This image failed to load. Please try again later.');
      return;
    }

    context.startTransition(`gallery-${image.id}`, `detail-${image.id}`, () => {
      navigation.navigate('Detail', { image });
    });
  }, [context, navigation, errors]);

  const handleImageLoadStart = useCallback((id: string) => {
    setLoading(prev => ({ ...prev, [id]: true }));
    setErrors(prev => ({ ...prev, [id]: false }));
  }, []);

  const handleImageLoadEnd = useCallback((id: string) => {
    setLoading(prev => ({ ...prev, [id]: false }));
  }, []);

  const handleImageError = useCallback((id: string) => {
    console.warn(`Image load error for ${id}`);
    setLoading(prev => ({ ...prev, [id]: false }));
    setErrors(prev => ({ ...prev, [id]: true }));
  }, []);

  const styles = createStyles(colorScheme ?? 'light');

  return (
    <ThemedView style={styles.container}>
      <ScrollView 
        style={styles.scrollView} 
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <ThemedText type="title" style={styles.title}>
          Image Gallery
        </ThemedText>
        <ThemedText style={styles.subtitle}>
          Tap any image to view in detail with smooth transitions
        </ThemedText>
        
        <View style={styles.imageGrid}>
          {images.map((image) => (
            <TouchableOpacity
              key={image.id}
              onPress={() => handleImagePress(image)}
              style={styles.imageContainer}
              activeOpacity={0.9}
              accessibilityRole="button"
              accessibilityLabel={`View ${image.title || 'image'} in detail`}
              accessibilityHint="Double tap to open image in full screen"
            >
              <SharedElement 
                id={`gallery-${image.id}`}
                accessibilityLabel={image.title}
              >
                <View style={styles.imageWrapper}>
                  {!errors[image.id] ? (
                    <Image 
                      source={{ uri: image.url }} 
                      style={styles.thumbnailImage}
                      onLoadStart={() => handleImageLoadStart(image.id)}
                      onLoad={() => handleImageLoadEnd(image.id)}
                      onError={() => handleImageError(image.id)}
                      contentFit="cover"
                      transition={200}
                    />
                  ) : (
                    <View style={styles.errorContainer}>
                      <ThemedText style={styles.errorText}>
                        Failed to load
                      </ThemedText>
                    </View>
                  )}
                  
                  {loading[image.id] && (
                    <View style={styles.loadingOverlay}>
                      <ActivityIndicator 
                        size="small" 
                        color={Colors[colorScheme ?? 'light'].tint} 
                      />
                    </View>
                  )}
                  
                  <View style={styles.imageOverlay}>
                    <ThemedText style={styles.imageTitle} numberOfLines={1}>
                      {image.title}
                    </ThemedText>
                  </View>
                </View>
              </SharedElement>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const createStyles = (colorScheme: 'light' | 'dark' | null) => StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 20,
  },
  title: {
    textAlign: 'center',
    marginVertical: 20,
    paddingHorizontal: 20,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 20,
    opacity: 0.7,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    paddingHorizontal: 10,
  },
  imageContainer: {
    width: SCREEN_WIDTH * 0.44,
    height: SCREEN_WIDTH * 0.44,
    margin: 5,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: Colors[colorScheme ?? 'light'].background,
    ...Platform.select({
      android: {
        elevation: 4,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
    }),
  },
  imageWrapper: {
    flex: 1,
    position: 'relative',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors[colorScheme ?? 'light'].background,
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.6,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  imageTitle: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});