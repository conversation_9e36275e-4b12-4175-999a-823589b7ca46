import { useRef, useState, useEffect } from 'react';
import { View } from 'react-native';
import { SharedElementData } from '@/components/SharedElement/types';
import { useSharedElementContext } from '@/components/SharedElement/SharedElementProvider';

/**
 * Hook for managing shared element registration and layout measurement
 * 
 * @param id - Unique identifier for the shared element
 * @param dependencies - Array of dependencies that trigger layout remeasurement
 * @returns Object containing viewRef and layout data
 */
export const useSharedElement = (id: string, dependencies: any[] = []) => {
  const context = useSharedElementContext();
  const viewRef = useRef<View>(null);
  const [layout, setLayout] = useState<SharedElementData | null>(null);

  useEffect(() => {
    let isMounted = true;
    let timeoutId: NodeJS.Timeout;

    const updateLayout = () => {
      if (viewRef.current && isMounted) {
        viewRef.current.measure((x, y, width, height, pageX, pageY) => {
          if (isMounted && width > 0 && height > 0) {
            setLayout({ x: pageX, y: pageY, width, height, id });
          }
        });
      }
    };

    // Initial measurement with delay to ensure layout is complete
    timeoutId = setTimeout(updateLayout, 100);
    
    // Poll for layout updates to improve reliability
    const interval = setInterval(updateLayout, 150);
    
    return () => {
      isMounted = false;
      clearInterval(interval);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [id, ...dependencies]);

  useEffect(() => {
    if (layout) {
      context.registerElement(id, layout);
    }

    return () => {
      context.unregisterElement(id);
    };
  }, [layout, id, context]);

  return { viewRef, layout };
};